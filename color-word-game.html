<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>看字说颜色游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .game-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }

        .game-title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .game-instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 1.1em;
            line-height: 1.6;
            color: #555;
        }

        .game-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .stat-item {
            padding: 10px 20px;
            border-radius: 10px;
            background: #e9ecef;
        }

        .timer {
            color: #dc3545;
        }

        .score {
            color: #28a745;
        }

        .word-display {
            font-size: 4em;
            font-weight: bold;
            margin: 40px 0;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px dashed #ddd;
            border-radius: 15px;
            background: #fafafa;
        }

        .color-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .color-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .color-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .color-btn:active {
            transform: translateY(0);
        }

        .control-buttons {
            margin-top: 20px;
        }

        .start-btn, .restart-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 10px;
            cursor: pointer;
            margin: 0 10px;
            transition: background 0.3s ease;
        }

        .start-btn:hover, .restart-btn:hover {
            background: #0056b3;
        }

        .game-over {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-size: 1.3em;
        }

        .hidden {
            display: none;
        }

        .feedback {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px 40px;
            border-radius: 10px;
            font-size: 1.5em;
            font-weight: bold;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }

        .feedback.wrong {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }

        .feedback.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1 class="game-title">看字说颜色</h1>
        
        <div class="game-instructions">
            <strong>游戏规则：</strong><br>
            屏幕上会显示一个颜色词汇，但显示的颜色可能与词汇含义不同。<br>
            请根据文字的<strong>实际显示颜色</strong>（而非文字内容）选择正确答案！<br>
            游戏时间：60秒
        </div>

        <div class="game-stats">
            <div class="stat-item timer">
                剩余时间: <span id="timeLeft">60</span>秒
            </div>
            <div class="stat-item score">
                正确: <span id="correct">0</span>
            </div>
            <div class="stat-item">
                错误: <span id="wrong">0</span>
            </div>
        </div>

        <div class="word-display" id="wordDisplay">
            点击开始游戏
        </div>

        <div class="color-buttons" id="colorButtons">
            <button class="color-btn" style="background-color: #ff0000;" onclick="selectColor('红色')">红色</button>
            <button class="color-btn" style="background-color: #ff8c00;" onclick="selectColor('橙色')">橙色</button>
            <button class="color-btn" style="background-color: #ffd700;" onclick="selectColor('黄色')">黄色</button>
            <button class="color-btn" style="background-color: #00ff00;" onclick="selectColor('绿色')">绿色</button>
            <button class="color-btn" style="background-color: #0000ff;" onclick="selectColor('蓝色')">蓝色</button>
            <button class="color-btn" style="background-color: #8a2be2;" onclick="selectColor('紫色')">紫色</button>
            <button class="color-btn" style="background-color: #000000;" onclick="selectColor('黑色')">黑色</button>
            <button class="color-btn" style="background-color: #ffffff; color: #333; text-shadow: none;" onclick="selectColor('白色')">白色</button>
            <button class="color-btn" style="background-color: #ff69b4;" onclick="selectColor('粉色')">粉色</button>
        </div>

        <div class="control-buttons">
            <button class="start-btn" id="startBtn" onclick="startGame()">开始游戏</button>
            <button class="restart-btn hidden" id="restartBtn" onclick="restartGame()">重新开始</button>
        </div>

        <div class="game-over hidden" id="gameOver">
            <h2>游戏结束！</h2>
            <p>最终得分：正确 <span id="finalCorrect">0</span> 次，错误 <span id="finalWrong">0</span> 次</p>
            <p>准确率：<span id="accuracy">0</span>%</p>
        </div>
    </div>

    <div class="feedback" id="feedback"></div>

    <script>
        // 游戏配置
        const colors = [
            { name: '红色', code: '#ff0000' },
            { name: '橙色', code: '#ff8c00' },
            { name: '黄色', code: '#ffd700' },
            { name: '绿色', code: '#00ff00' },
            { name: '蓝色', code: '#0000ff' },
            { name: '紫色', code: '#8a2be2' },
            { name: '黑色', code: '#000000' },
            { name: '白色', code: '#ffffff' },
            { name: '粉色', code: '#ff69b4' }
        ];

        // 游戏状态
        let gameState = {
            isPlaying: false,
            timeLeft: 60,
            correct: 0,
            wrong: 0,
            currentWord: '',
            currentColor: '',
            timer: null
        };

        // DOM元素
        const elements = {
            wordDisplay: document.getElementById('wordDisplay'),
            timeLeft: document.getElementById('timeLeft'),
            correct: document.getElementById('correct'),
            wrong: document.getElementById('wrong'),
            startBtn: document.getElementById('startBtn'),
            restartBtn: document.getElementById('restartBtn'),
            gameOver: document.getElementById('gameOver'),
            finalCorrect: document.getElementById('finalCorrect'),
            finalWrong: document.getElementById('finalWrong'),
            accuracy: document.getElementById('accuracy'),
            feedback: document.getElementById('feedback'),
            colorButtons: document.getElementById('colorButtons')
        };

        // 开始游戏
        function startGame() {
            gameState.isPlaying = true;
            gameState.timeLeft = 60;
            gameState.correct = 0;
            gameState.wrong = 0;
            
            elements.startBtn.classList.add('hidden');
            elements.restartBtn.classList.remove('hidden');
            elements.gameOver.classList.add('hidden');
            elements.colorButtons.style.pointerEvents = 'auto';
            
            updateDisplay();
            generateNewWord();
            startTimer();
        }

        // 重新开始游戏
        function restartGame() {
            clearInterval(gameState.timer);
            startGame();
        }

        // 开始计时器
        function startTimer() {
            gameState.timer = setInterval(() => {
                gameState.timeLeft--;
                elements.timeLeft.textContent = gameState.timeLeft;
                
                if (gameState.timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        // 生成新的词汇和颜色
        function generateNewWord() {
            if (!gameState.isPlaying) return;
            
            const wordColor = colors[Math.floor(Math.random() * colors.length)];
            const displayColor = colors[Math.floor(Math.random() * colors.length)];
            
            gameState.currentWord = wordColor.name;
            gameState.currentColor = displayColor.name;
            
            elements.wordDisplay.textContent = wordColor.name;
            elements.wordDisplay.style.color = displayColor.code;
            
            // 如果是白色，添加黑色边框以便看清
            if (displayColor.name === '白色') {
                elements.wordDisplay.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)';
            } else {
                elements.wordDisplay.style.textShadow = 'none';
            }
        }

        // 选择颜色
        function selectColor(selectedColor) {
            if (!gameState.isPlaying) return;
            
            const isCorrect = selectedColor === gameState.currentColor;
            
            if (isCorrect) {
                gameState.correct++;
                showFeedback('正确！', 'correct');
            } else {
                gameState.wrong++;
                showFeedback('错误！', 'wrong');
            }
            
            updateDisplay();
            
            // 延迟生成下一个词汇
            setTimeout(() => {
                generateNewWord();
            }, 500);
        }

        // 显示反馈
        function showFeedback(message, type) {
            elements.feedback.textContent = message;
            elements.feedback.className = `feedback ${type} show`;
            
            setTimeout(() => {
                elements.feedback.classList.remove('show');
            }, 500);
        }

        // 更新显示
        function updateDisplay() {
            elements.correct.textContent = gameState.correct;
            elements.wrong.textContent = gameState.wrong;
        }

        // 结束游戏
        function endGame() {
            gameState.isPlaying = false;
            clearInterval(gameState.timer);
            
            elements.colorButtons.style.pointerEvents = 'none';
            elements.wordDisplay.textContent = '游戏结束';
            elements.wordDisplay.style.color = '#333';
            elements.wordDisplay.style.textShadow = 'none';
            
            const total = gameState.correct + gameState.wrong;
            const accuracy = total > 0 ? Math.round((gameState.correct / total) * 100) : 0;
            
            elements.finalCorrect.textContent = gameState.correct;
            elements.finalWrong.textContent = gameState.wrong;
            elements.accuracy.textContent = accuracy;
            elements.gameOver.classList.remove('hidden');
        }

        // 初始化
        elements.colorButtons.style.pointerEvents = 'none';
    </script>
</body>
</html>
